#!/usr/bin/env python3
"""
Test Coinbase Advanced Client with Fallback Authentication
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_environment():
    """Load environment variables from .env file"""
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        logger.info(f"✅ Loaded environment from {env_file}")
    else:
        logger.warning(f"⚠️ No .env file found at {env_file}")

async def test_coinbase_advanced_client():
    """Test the Coinbase Advanced Client with fallback authentication"""
    logger.info("🚀 Testing Coinbase Advanced Client...")
    
    try:
        # Load environment
        load_environment()
        
        # Import the advanced client
        from exchanges.coinbase_advanced_client import CoinbaseAdvancedClient
        
        # Initialize the client
        client = CoinbaseAdvancedClient()
        
        # Initialize with fallback authentication
        logger.info("🔄 Initializing client with fallback authentication...")
        success = await client.initialize()
        
        if success:
            logger.info("✅ Client initialization successful!")
            
            # Get authentication status
            auth_status = client.get_authentication_status()
            logger.info(f"📊 Authentication method: {auth_status['current_method']}")
            logger.info(f"📊 Authenticated: {auth_status['authenticated']}")
            logger.info(f"📊 Capabilities: {auth_status['capabilities']}")
            logger.info(f"📊 Read-only mode: {auth_status['read_only_mode']}")
            
            # Test getting balances
            if auth_status['authenticated']:
                logger.info("🔄 Testing balance retrieval...")
                try:
                    balances = client.get_all_balances()
                    logger.info(f"📊 Balances: {balances}")
                    
                    # Check if we have any EUR balance
                    if 'EUR' in balances.get('balances', {}):
                        eur_balance = balances['balances']['EUR']
                        logger.info(f"💰 EUR Balance: {eur_balance}")
                    
                except Exception as e:
                    logger.error(f"❌ Balance retrieval failed: {e}")
            
            # Test getting products (should work even in read-only mode)
            logger.info("🔄 Testing product listing...")
            try:
                products = await client.get_products()
                if products:
                    logger.info(f"📊 Found {len(products)} products")
                    # Show a few EUR pairs
                    eur_pairs = [p for p in products if 'EUR' in p.get('product_id', '')][:5]
                    for pair in eur_pairs:
                        logger.info(f"  - {pair.get('product_id')}: {pair.get('status')}")
                else:
                    logger.warning("⚠️ No products returned")
            except Exception as e:
                logger.error(f"❌ Product listing failed: {e}")
            
            return True
        else:
            logger.error("❌ Client initialization failed")
            error_msg = client.get_error_message()
            logger.error(f"❌ Error: {error_msg}")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("💡 Trying alternative import path...")
        
        try:
            from src.exchanges.coinbase_advanced_client import CoinbaseAdvancedClient
            logger.info("✅ Alternative import successful")
            # Retry with correct import
            return await test_coinbase_advanced_client()
        except ImportError as e2:
            logger.error(f"❌ Alternative import also failed: {e2}")
            return False
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_coinbase_client():
    """Test the basic Coinbase client"""
    logger.info("🚀 Testing Basic Coinbase Client...")
    
    try:
        from exchanges.coinbase import CoinbaseClient
        
        # Initialize the client
        client = CoinbaseClient()
        
        # Test initialization
        success = await client.initialize()
        
        if success:
            logger.info("✅ Basic client initialization successful!")
            
            # Test getting balances
            try:
                balances = await client.get_balances()
                logger.info(f"📊 Basic client balances: {balances}")
                return True
            except Exception as e:
                logger.error(f"❌ Basic client balance retrieval failed: {e}")
                return False
        else:
            logger.error("❌ Basic client initialization failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Basic client test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🔍 Coinbase Client Comprehensive Test")
    logger.info("=" * 60)
    
    # Test advanced client first
    advanced_success = await test_coinbase_advanced_client()
    
    logger.info("\n" + "=" * 60)
    
    # Test basic client
    basic_success = await test_basic_coinbase_client()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"  Advanced Client: {'✅ PASS' if advanced_success else '❌ FAIL'}")
    logger.info(f"  Basic Client: {'✅ PASS' if basic_success else '❌ FAIL'}")
    
    if advanced_success or basic_success:
        logger.info("🎉 At least one Coinbase client is working!")
        return True
    else:
        logger.error("❌ All Coinbase clients failed")
        logger.info("💡 Recommendations:")
        logger.info("  1. Check API key permissions in Coinbase dashboard")
        logger.info("  2. Verify account is not restricted")
        logger.info("  3. Consider generating new API credentials")
        return False

if __name__ == "__main__":
    asyncio.run(main())
