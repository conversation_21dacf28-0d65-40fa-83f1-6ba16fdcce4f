import jwt
from cryptography.hazmat.primitives import serialization
import time
import secrets
import http.client
import os
import json
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_environment():
    """Load environment variables from .env file"""
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        logger.info(f"✅ Loaded environment from {env_file}")
    else:
        logger.warning(f"⚠️ No .env file found at {env_file}")

def load_credentials():
    """Load Coinbase CDP credentials from environment"""
    try:
        # Load environment first
        load_environment()

        # Try to load from encrypted environment variables first
        try:
            from utils.cryptpography.hybrid import HybridCrypto
            crypto = HybridCrypto()

            encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
            encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')

            if encrypted_api_key and encrypted_private_key:
                api_key_name = crypto.decrypt_value(encrypted_api_key)
                private_key = crypto.decrypt_value(encrypted_private_key)
                logger.info("✅ Loaded encrypted credentials")
                logger.info(f"📊 API Key: {api_key_name}")
                logger.info(f"📊 Private Key: {private_key[:50]}...")
                return api_key_name, private_key
        except ImportError as e:
            logger.warning(f"⚠️ Cryptography module not available: {e}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to decrypt credentials: {e}")

        # Fallback to plain environment variables
        api_key_name = os.getenv('COINBASE_API_KEY_NAME')
        private_key = os.getenv('COINBASE_PRIVATE_KEY')

        if not api_key_name or not private_key:
            # Try newer API key from .env comments
            api_key_name = "organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/6548b65d-0ea5-40f0-b2ac-c0c89ca1bcd2"
            private_key = os.getenv('COINBASE_PRIVATE_KEY')  # Still need this from env
            logger.info("⚠️ Using newer API key from .env comments")

        if not api_key_name or not private_key:
            raise ValueError("Missing Coinbase credentials - set COINBASE_API_KEY_NAME and COINBASE_PRIVATE_KEY")

        logger.info("✅ Loaded plain credentials")
        return api_key_name, private_key

    except Exception as e:
        logger.error(f"❌ Failed to load credentials: {e}")
        raise

def build_jwt(uri, key_name, key_secret):
    """Build JWT token exactly as provided by Coinbase support"""
    private_key_bytes = key_secret.encode('utf-8')
    private_key = serialization.load_pem_private_key(private_key_bytes, password=None)

    jwt_payload = {
        'sub': key_name,
        'iss': "cdp",
        'nbf': int(time.time()),
        'exp': int(time.time()) + 120,
        'uri': uri,
    }

    # Debug: Print payload details
    logger.info(f"📊 JWT Payload: {jwt_payload}")

    jwt_token = jwt.encode(
        jwt_payload,
        private_key,
        algorithm='ES256',
        headers={'kid': key_name, 'nonce': secrets.token_hex()},
    )

    # Debug: Decode and print token details
    try:
        import base64
        header, payload, signature = jwt_token.split('.')

        # Decode header
        header_decoded = json.loads(base64.urlsafe_b64decode(header + '=='))
        logger.info(f"📊 JWT Header: {header_decoded}")

        # Decode payload
        payload_decoded = json.loads(base64.urlsafe_b64decode(payload + '=='))
        logger.info(f"📊 JWT Payload Decoded: {payload_decoded}")

    except Exception as e:
        logger.warning(f"⚠️ Could not decode JWT for debugging: {e}")

    return jwt_token

def test_api_key(api_key_name, private_key, description):
    """Test a specific API key with the support implementation"""
    logger.info(f"🔄 Testing {description}...")
    logger.info(f"📊 API Key: {api_key_name}")

    try:
        # Test configuration exactly as in support code
        request_method = "GET"
        request_host = "api.coinbase.com"
        request_path = "/api/v3/brokerage/accounts"

        # Build URI exactly as in support code
        uri = f"{request_method} {request_host}{request_path}"

        # Generate JWT token
        jwt_token = build_jwt(uri, api_key_name, private_key)
        logger.info(f"✅ JWT token generated: {jwt_token[:50]}...")

        # Make request using http.client exactly as in support code
        conn = http.client.HTTPSConnection("api.coinbase.com")
        payload = ''
        headers = {
            'Content-Type': 'application/json',
            'Authorization': "Bearer " + jwt_token
        }

        logger.info("🔄 Making request to /api/v3/brokerage/accounts...")
        conn.request("GET", "/api/v3/brokerage/accounts", payload, headers)
        res = conn.getresponse()
        data = res.read()

        logger.info(f"📊 Response Status: {res.status}")

        response_text = data.decode("utf-8")
        logger.info(f"📊 Response Body: {response_text}")

        if res.status == 200:
            logger.info(f"✅ SUCCESS: {description} working!")
            try:
                response_json = json.loads(response_text)
                accounts = response_json.get('accounts', [])
                logger.info(f"📊 Found {len(accounts)} accounts")
                for account in accounts:
                    currency = account.get('currency', 'Unknown')
                    balance = account.get('available_balance', {}).get('value', '0')
                    logger.info(f"  - {currency}: {balance}")
            except json.JSONDecodeError:
                logger.warning("⚠️ Response is not valid JSON")
        else:
            logger.error(f"❌ FAILED: {description} - HTTP {res.status} - {response_text}")

        conn.close()
        return res.status == 200

    except Exception as e:
        logger.error(f"❌ Test failed for {description}: {e}")
        return False

def test_coinbase_support_implementation():
    """Test the exact implementation provided by Coinbase support"""
    logger.info("🔄 Testing Coinbase support implementation...")

    try:
        # Load credentials
        key_name, key_secret = load_credentials()

        # Test the loaded credentials first
        success1 = test_api_key(key_name, key_secret, "Current encrypted credentials")

        # Test with the old API key from support conversation
        old_api_key = "organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/b71fc94b-f040-4d88-9435-7ee897421f33"
        success2 = test_api_key(old_api_key, key_secret, "Old API key from support")

        # Test with the newer API key from .env comments
        new_api_key = "organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/6548b65d-0ea5-40f0-b2ac-c0c89ca1bcd2"
        success3 = test_api_key(new_api_key, key_secret, "Newer API key from .env")

        return success1 or success2 or success3

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def test_public_endpoint():
    """Test public endpoint to verify basic connectivity"""
    logger.info("🔄 Testing public endpoint...")
    
    try:
        conn = http.client.HTTPSConnection("api.coinbase.com")
        conn.request("GET", "/api/v3/brokerage/time")
        res = conn.getresponse()
        data = res.read()
        
        logger.info(f"📊 Public endpoint status: {res.status}")
        if res.status == 200:
            response_text = data.decode("utf-8")
            logger.info(f"📊 Public endpoint response: {response_text}")
            logger.info("✅ Public endpoint working")
        else:
            logger.error(f"❌ Public endpoint failed: {res.status}")
            
        conn.close()
        return res.status == 200
        
    except Exception as e:
        logger.error(f"❌ Public endpoint test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting Coinbase Support Test...")
    
    # Test public endpoint first
    public_success = test_public_endpoint()
    
    # Test private endpoint with support implementation
    private_success = test_coinbase_support_implementation()
    
    logger.info("=" * 60)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"  Public endpoint: {'✅ PASS' if public_success else '❌ FAIL'}")
    logger.info(f"  Private endpoint: {'✅ PASS' if private_success else '❌ FAIL'}")
    
    if private_success:
        logger.info("🎉 Coinbase authentication is working correctly!")
    else:
        logger.error("❌ Coinbase authentication still failing")
        logger.info("💡 Next steps:")
        logger.info("  1. Verify API key permissions in Coinbase dashboard")
        logger.info("  2. Check for account restrictions")
        logger.info("  3. Contact Coinbase support with test results")
