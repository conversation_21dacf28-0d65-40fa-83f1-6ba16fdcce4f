import asyncio
import hashlib
import hmac
import base64
import time
import json
import secrets
from decimal import Decimal
from typing import Dict, List, Optional, Any
import aiohttp
import logging
from urllib.parse import urlencode
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec

logger = logging.getLogger(__name__)

class CoinbaseClient:
    """Coinbase Developer Platform (CDP) API client with JWT authentication"""
    
    def __init__(self, api_key_name: str, private_key_pem: str, sandbox: bool = False):
        self.api_key_name = api_key_name
        self.private_key_pem = private_key_pem
        self.sandbox = sandbox
        
        # Use CDP API endpoints
        if sandbox:
            self.base_url = "https://api.coinbase.com"  # CDP uses same endpoint for both
        else:
            self.base_url = "https://api.coinbase.com"
            
        self.session = None
        self._private_key = None
        self._load_private_key()
        logger.info(f"Coinbase CDP Client initialized ({'sandbox' if sandbox else 'live'} mode)")
    
    def _load_private_key(self):
        """Load the EC private key from PEM string"""
        try:
            self._private_key = serialization.load_pem_private_key(
                self.private_key_pem.encode('utf-8'),
                password=None
            )
            if not isinstance(self._private_key, ec.EllipticCurvePrivateKey):
                raise ValueError("Private key must be an EC private key")
            logger.info("EC private key loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load private key: {e}")
            raise
    
    def _create_jwt_token(self, method: str, endpoint: str) -> str:
        """Create JWT token using EXACT implementation from Coinbase support"""
        try:
            # Create JWT payload - exactly as per Coinbase support code
            now = int(time.time())
            payload = {
                'sub': self.api_key_name,        # Subject: full API key name
                'iss': 'cdp',                    # Issuer: Coinbase Developer Platform
                'nbf': now,                      # Not before: current timestamp
                'exp': now + 120,                # Expires: 2 minutes from now
                'uri': f'{method} api.coinbase.com{endpoint}'  # URI: exactly as in support code
            }

            # Generate JWT token using exact Coinbase support implementation
            # Note: kid uses FULL API key name, not just extracted key ID
            # Note: nonce is included as per support code
            token = jwt.encode(
                payload,
                self._private_key,
                algorithm='ES256',
                headers={
                    'kid': self.api_key_name,    # Full API key name as kid
                    'nonce': secrets.token_hex() # Random nonce for security
                }
            )

            logger.debug(f"JWT token created for {method} {endpoint}")
            return token
        except Exception as e:
            logger.error(f"Failed to create JWT token: {e}")
            raise
    
    def _get_headers(self, method: str, path: str) -> Dict[str, str]:
        """Generate headers using EXACT implementation from Coinbase support"""
        # Create JWT token with method and endpoint separately (as in support code)
        jwt_token = self._create_jwt_token(method, path)

        # Use the exact headers format from Coinbase support
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {jwt_token}'
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize the HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            logger.info("Coinbase CDP client session initialized")
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Coinbase CDP client session closed")
    
    async def _make_request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """Make authenticated request to Coinbase CDP API"""
        if not self.session:
            await self.initialize()
        
        url = f"{self.base_url}{endpoint}"
        
        # Prepare query string for GET requests
        query_string = ''
        if method == 'GET' and params:
            query_string = '?' + urlencode(params)
            url += query_string
        
        # Prepare body for POST requests
        body = None
        if method == 'POST' and data:
            body = json.dumps(data)
        
        # Generate headers with JWT token
        headers = self._get_headers(method, endpoint + query_string)
        
        try:
            if method == 'GET':
                async with self.session.get(url, headers=headers) as response:
                    return await self._handle_response(response)
            elif method == 'POST':
                async with self.session.post(url, headers=headers, data=body) as response:
                    return await self._handle_response(response)
            elif method == 'DELETE':
                async with self.session.delete(url, headers=headers) as response:
                    return await self._handle_response(response)
        except Exception as e:
            logger.error(f"Coinbase CDP API request failed: {e}")
            raise
    
    async def _handle_response(self, response: aiohttp.ClientResponse) -> Dict:
        """Handle API response and errors"""
        try:
            data = await response.json()
            
            if response.status == 200:
                return data
            else:
                error_msg = data.get('message', f'HTTP {response.status}')
                logger.error(f"Coinbase CDP API error: {error_msg}")
                raise Exception(f"Coinbase CDP API error: {error_msg}")
                
        except json.JSONDecodeError:
            text = await response.text()
            logger.error(f"Invalid JSON response: {text}")
            raise Exception(f"Invalid JSON response from Coinbase CDP API")
    
    async def get_accounts(self) -> List[Dict]:
        """Get all accounts using CDP API"""
        return await self._make_request('GET', '/api/v3/brokerage/accounts')
    
    async def get_account(self, account_uuid: str) -> Dict:
        """Get specific account details using CDP API"""
        return await self._make_request('GET', f'/api/v3/brokerage/accounts/{account_uuid}')
    
    async def get_products(self) -> List[Dict]:
        """Get available trading pairs using CDP API"""
        return await self._make_request('GET', '/api/v3/brokerage/products')
    
    async def get_product(self, product_id: str) -> Dict:
        """Get specific product details using CDP API"""
        return await self._make_request('GET', f'/api/v3/brokerage/products/{product_id}')
    
    async def get_best_bid_ask(self, product_ids: List[str]) -> Dict:
        """Get best bid/ask for products using CDP API"""
        params = {'product_ids': ','.join(product_ids)}
        return await self._make_request('GET', '/api/v3/brokerage/best_bid_ask', params=params)
    
    async def get_product_book(self, product_id: str, limit: int = 25) -> Dict:
        """Get product order book using CDP API"""
        params = {'limit': limit}
        return await self._make_request('GET', f'/api/v3/brokerage/product_book', params=params)
    
    async def create_order(self, client_order_id: str, product_id: str, side: str, 
                          order_configuration: Dict) -> Dict:
        """Create an order using CDP API"""
        order_data = {
            'client_order_id': client_order_id,
            'product_id': product_id,
            'side': side.upper(),
            'order_configuration': order_configuration
        }
        
        return await self._make_request('POST', '/api/v3/brokerage/orders', data=order_data)
    
    async def cancel_orders(self, order_ids: List[str]) -> Dict:
        """Cancel orders using CDP API"""
        data = {'order_ids': order_ids}
        return await self._make_request('POST', '/api/v3/brokerage/orders/batch_cancel', data=data)
    
    async def list_orders(self, product_id: str = None, order_status: List[str] = None, 
                         limit: int = 100, start_date: str = None, end_date: str = None) -> Dict:
        """List orders using CDP API"""
        params = {'limit': limit}
        if product_id:
            params['product_id'] = product_id
        if order_status:
            params['order_status'] = order_status
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
            
        return await self._make_request('GET', '/api/v3/brokerage/orders/historical/batch', params=params)
    
    async def list_fills(self, order_id: str = None, product_id: str = None, 
                        start_sequence_timestamp: str = None, end_sequence_timestamp: str = None,
                        limit: int = 100) -> Dict:
        """List fills using CDP API"""
        params = {'limit': limit}
        if order_id:
            params['order_id'] = order_id
        if product_id:
            params['product_id'] = product_id
        if start_sequence_timestamp:
            params['start_sequence_timestamp'] = start_sequence_timestamp
        if end_sequence_timestamp:
            params['end_sequence_timestamp'] = end_sequence_timestamp
            
        return await self._make_request('GET', '/api/v3/brokerage/orders/historical/fills', params=params)
    
    async def get_balances(self) -> Dict[str, Decimal]:
        """Get account balances for all currencies using CDP API"""
        try:
            response = await self.get_accounts()
            balances = {}
            
            accounts = response.get('accounts', [])
            for account in accounts:
                currency = account.get('currency', '')
                available_balance = account.get('available_balance', {})
                balance_value = available_balance.get('value', '0')
                balance = Decimal(balance_value)
                if balance > 0:
                    balances[currency] = balance
            
            logger.info(f"Retrieved balances for {len(balances)} currencies")
            return balances
            
        except Exception as e:
            logger.error(f"Failed to get balances: {e}")
            return {}
    
    # Legacy method names for backward compatibility
    async def get_product_ticker(self, product_id: str) -> Dict:
        """Get ticker data for a product (legacy compatibility)"""
        return await self.get_best_bid_ask([product_id])
    
    async def get_order_book(self, product_id: str, level: int = 2) -> Dict:
        """Get order book for a product (legacy compatibility)"""
        limit = 25 if level == 2 else 50
        return await self.get_product_book(product_id, limit)
    
    async def place_order(self, product_id: str, side: str, order_type: str, 
                         size: str = None, price: str = None, funds: str = None) -> Dict:
        """Place a trading order (legacy compatibility)"""
        import uuid
        client_order_id = str(uuid.uuid4())
        
        if order_type.lower() == 'limit':
            if not (size and price):
                raise ValueError("Limit orders require both size and price")
            order_configuration = {
                'limit_limit_gtc': {
                    'base_size': size,
                    'limit_price': price
                }
            }
        elif order_type.lower() == 'market':
            if size:
                order_configuration = {
                    'market_market_ioc': {
                        'base_size': size
                    }
                }
            elif funds:
                order_configuration = {
                    'market_market_ioc': {
                        'quote_size': funds
                    }
                }
            else:
                raise ValueError("Market orders require either size or funds")
        else:
            raise ValueError(f"Unsupported order type: {order_type}")
        
        return await self.create_order(client_order_id, product_id, side, order_configuration)
    
    async def get_order(self, order_id: str) -> Dict:
        """Get order details (legacy compatibility)"""
        orders = await self.list_orders()
        for order in orders.get('orders', []):
            if order.get('order_id') == order_id:
                return order
        raise Exception(f"Order {order_id} not found")
    
    async def cancel_order(self, order_id: str) -> Dict:
        """Cancel an order (legacy compatibility)"""
        return await self.cancel_orders([order_id])
    
    async def get_fills(self, product_id: str = None) -> List[Dict]:
        """Get trade fills (legacy compatibility)"""
        response = await self.list_fills(product_id=product_id)
        return response.get('fills', [])
    
    async def execute_order(self, symbol: str, side: str, amount: str,
                           order_type: str = 'market', price: str = None) -> Dict:
        """Execute a trading order with proper error handling"""
        try:
            # Convert symbol format (BTC/USDT -> BTC-USDT)
            product_id = symbol.replace('/', '-')
            
            order_result = await self.place_order(
                product_id=product_id,
                side=side,
                order_type=order_type,
                size=amount,
                price=price
            )
            
            logger.info(f"Coinbase order executed: {order_result.get('id', 'unknown')}")
            return {
                'order_id': order_result.get('id'),
                'status': order_result.get('status'),
                'price': order_result.get('price'),
                'size': order_result.get('size'),
                'side': order_result.get('side'),
                'product_id': order_result.get('product_id')
            }
            
        except Exception as e:
            logger.error(f"Coinbase order execution failed: {e}")
            raise

    async def test_connection(self) -> bool:
        """Test API connection and authentication"""
        try:
            accounts = await self.get_accounts()
            logger.info("Coinbase connection test successful")
            return True
        except Exception as e:
            logger.error(f"Coinbase connection test failed: {e}")
            return False
